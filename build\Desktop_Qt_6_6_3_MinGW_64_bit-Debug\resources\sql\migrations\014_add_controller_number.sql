-- 迁移文件：014_add_controller_number.sql
-- 描述：为controllers表添加controller_number字段
-- 创建时间：2025-07-30
-- 作者：AI Assistant

-- ========== 为controllers表添加controller_number字段 ==========

-- 添加controller_number字段
ALTER TABLE controllers ADD COLUMN controller_number INTEGER;

-- 为现有记录设置controller_number（使用id作为默认值）
UPDATE controllers SET controller_number = id WHERE controller_number IS NULL;

-- 设置controller_number为NOT NULL和UNIQUE
-- 注意：SQLite不支持直接修改列约束，需要重建表
-- 这里先创建临时表，然后复制数据

-- 创建临时表
CREATE TABLE controllers_temp (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    controller_number INTEGER UNIQUE NOT NULL,  -- 控制器编号
    serial_number VARCHAR(50) UNIQUE NOT NULL,  -- 序列号SN
    enabled INTEGER NOT NULL DEFAULT 1,         -- 是否启用
    ip_address VARCHAR(15),                     -- IP地址
    port INTEGER DEFAULT 60000,                 -- 端口号
    area_id INTEGER,                            -- 所在区域ID
    description TEXT,                           -- 说明
    model VARCHAR(50),                          -- 型号
    firmware_version VARCHAR(20),               -- 固件版本
    hardware_version VARCHAR(20),               -- 硬件版本
    max_doors INTEGER DEFAULT 4,                -- 最大门数
    max_readers INTEGER DEFAULT 8,              -- 最大读卡器数
    online_status INTEGER DEFAULT 0,            -- 在线状态：0-离线，1-在线
    last_online_time TIMESTAMP,                 -- 最后在线时间
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (area_id) REFERENCES areas(id) ON DELETE SET NULL
);

-- 复制数据到临时表
INSERT INTO controllers_temp (
    id, controller_number, serial_number, enabled, ip_address, port, area_id, 
    description, model, firmware_version, hardware_version, max_doors, max_readers, 
    online_status, last_online_time, created_at, updated_at
)
SELECT 
    id, controller_number, serial_number, enabled, ip_address, port, area_id, 
    description, model, firmware_version, hardware_version, max_doors, max_readers, 
    online_status, last_online_time, created_at, updated_at
FROM controllers;

-- 删除原表
DROP TABLE controllers;

-- 重命名临时表
ALTER TABLE controllers_temp RENAME TO controllers;

-- 重新创建索引
CREATE INDEX IF NOT EXISTS idx_controllers_serial_number ON controllers(serial_number);
CREATE INDEX IF NOT EXISTS idx_controllers_ip_address ON controllers(ip_address);
CREATE INDEX IF NOT EXISTS idx_controllers_area_id ON controllers(area_id);
CREATE INDEX IF NOT EXISTS idx_controllers_online_status ON controllers(online_status);
CREATE INDEX IF NOT EXISTS idx_controllers_controller_number ON controllers(controller_number);

-- 重新创建触发器
CREATE TRIGGER IF NOT EXISTS trigger_controllers_update 
    AFTER UPDATE ON controllers
    FOR EACH ROW
BEGIN
    UPDATE controllers SET updated_at = CURRENT_TIMESTAMP WHERE id = OLD.id;
END; 
-- 迁移文件：005_create_controllers_table.sql
-- 描述：创建门禁控制器表
-- 创建时间：2024-07-10
-- 作者：AI Assistant

-- ========== 创建controllers表 ==========

CREATE TABLE IF NOT EXISTS controllers (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    controller_number INTEGER UNIQUE NOT NULL,  -- 控制器编号
    serial_number VARCHAR(50) UNIQUE NOT NULL,  -- 序列号SN
    enabled INTEGER NOT NULL DEFAULT 1,         -- 是否启用
    ip_address VARCHAR(15),                     -- IP地址
    port INTEGER DEFAULT 60000,                 -- 端口号
    area_id INTEGER,                            -- 所在区域ID
    description TEXT,                           -- 说明
    model VARCHAR(50),                          -- 型号
    firmware_version VARCHAR(20),               -- 固件版本
    hardware_version VARCHAR(20),               -- 硬件版本
    max_doors INTEGER DEFAULT 4,                -- 最大门数
    max_readers INTEGER DEFAULT 8,              -- 最大读卡器数
    online_status INTEGER DEFAULT 0,            -- 在线状态：0-离线，1-在线
    last_online_time TIMESTAMP,                 -- 最后在线时间
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (area_id) REFERENCES areas(id) ON DELETE SET NULL
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_controllers_serial_number ON controllers(serial_number);
CREATE INDEX IF NOT EXISTS idx_controllers_ip_address ON controllers(ip_address);
CREATE INDEX IF NOT EXISTS idx_controllers_area_id ON controllers(area_id);
CREATE INDEX IF NOT EXISTS idx_controllers_online_status ON controllers(online_status);

-- 创建触发器，在更新controllers时自动更新updated_at字段
CREATE TRIGGER IF NOT EXISTS trigger_controllers_update 
    AFTER UPDATE ON controllers
    FOR EACH ROW
BEGIN
    UPDATE controllers SET updated_at = CURRENT_TIMESTAMP WHERE id = OLD.id;
END; 
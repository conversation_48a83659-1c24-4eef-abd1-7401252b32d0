#include "Operator.h"
#include <QCryptographicHash>
#include <QUuid>

namespace AccessControl {

Operator::Operator()
    : m_id(-1),
      m_role(Role::Viewer),
      m_status(Status::Inactive),
      m_loginAttempts(0),
      m_accessEnabled(true),
      m_attendanceEnabled(true),
      m_validFrom(QDate::currentDate()),
      m_validUntil(QDate(2099, 12, 31)),
      m_departmentId(-1),
      m_shiftWork(false) {}

int Operator::id() const { return m_id; }
void Operator::setId(int id) { m_id = id; }

const QString& Operator::username() const { return m_username; }
void Operator::setUsername(const QString& username) { m_username = username; }

const QString& Operator::passwordHash() const { return m_passwordHash; }
void Operator::setPasswordHash(const QString& passwordHash) { m_passwordHash = passwordHash; }

const QString& Operator::salt() const { return m_salt; }
void Operator::setSalt(const QString& salt) { m_salt = salt; }

Operator::Role Operator::role() const { return m_role; }
void Operator::setRole(Role role) { m_role = role; }

Operator::Status Operator::status() const { return m_status; }
void Operator::setStatus(Status status) { m_status = status; }

const QString& Operator::realName() const { return m_realName; }
void Operator::setRealName(const QString& realName) {
    m_realName = realName.isNull() ? m_username : realName;
}

const QString& Operator::email() const { return m_email; }
void Operator::setEmail(const QString& email) { m_email = email; }

const QDateTime& Operator::createdAt() const { return m_createdAt; }
void Operator::setCreatedAt(const QDateTime& createdAt) {
    m_createdAt = createdAt.isValid() ? createdAt : QDateTime::currentDateTime();
}

const QDateTime& Operator::updatedAt() const { return m_updatedAt; }
void Operator::setUpdatedAt(const QDateTime& updatedAt) {
    m_updatedAt = updatedAt.isValid() ? updatedAt : QDateTime::currentDateTime();
}

const QDateTime& Operator::lastLoginAt() const { return m_lastLoginAt; }
void Operator::setLastLoginAt(const QDateTime& lastLoginAt) { m_lastLoginAt = lastLoginAt; }

const QString& Operator::lastLoginIp() const { return m_lastLoginIp; }
void Operator::setLastLoginIp(const QString& lastLoginIp) { m_lastLoginIp = lastLoginIp; }

int Operator::loginAttempts() const { return m_loginAttempts; }
void Operator::setLoginAttempts(int loginAttempts) { m_loginAttempts = loginAttempts; }

const QString& Operator::phoneNumber() const { return m_phoneNumber; }
void Operator::setPhoneNumber(const QString& phoneNumber) { m_phoneNumber = phoneNumber; }

const QString& Operator::idNumber() const { return m_idNumber; }
void Operator::setIdNumber(const QString& idNumber) { m_idNumber = idNumber; }

bool Operator::accessEnabled() const { return m_accessEnabled; }
void Operator::setAccessEnabled(bool enabled) { m_accessEnabled = enabled; }

bool Operator::attendanceEnabled() const { return m_attendanceEnabled; }
void Operator::setAttendanceEnabled(bool enabled) { m_attendanceEnabled = enabled; }

const QDate& Operator::validFrom() const { return m_validFrom; }
void Operator::setValidFrom(const QDate& validFrom) { m_validFrom = validFrom; }

const QDate& Operator::validUntil() const { return m_validUntil; }
void Operator::setValidUntil(const QDate& validUntil) { m_validUntil = validUntil; }

int Operator::departmentId() const { return m_departmentId; }
void Operator::setDepartmentId(int departmentId) { m_departmentId = departmentId; }

const QString& Operator::workNumber() const { return m_workNumber; }
void Operator::setWorkNumber(const QString& workNumber) { m_workNumber = workNumber; }

bool Operator::shiftWork() const { return m_shiftWork; }
void Operator::setShiftWork(bool shiftWork) { m_shiftWork = shiftWork; }

void Operator::setPassword(const QString& password) {
    m_salt = QUuid::createUuid().toString(QUuid::WithoutBraces);
    m_passwordHash = hashPassword(password, m_salt);
}

bool Operator::checkPassword(const QString& password) const {
    QString calculatedHash = hashPassword(password, m_salt);
    qDebug() << "Operator::checkPassword for Operator:" << m_Operatorname;
    qDebug() << "Input password:" << password;
    qDebug() << "Salt:" << m_salt;
    qDebug() << "Stored hash:" << m_passwordHash;
    qDebug() << "Calculated hash:" << calculatedHash;
    qDebug() << "Match:" << (m_passwordHash == calculatedHash);
    return m_passwordHash == calculatedHash;
}

QString Operator::hashPassword(const QString& password, const QString& salt) {
    QByteArray data = (password + salt).toUtf8();
    return QCryptographicHash::hash(data, QCryptographicHash::Sha256).toHex();
}

QString Operator::roleToString(Role role) {
    switch (role) {
        case Role::SuperAdmin: return "超级管理员";
        case Role::Admin: return "管理员";
        case Role::Operator: return "操作员";
        case Role::Viewer: return "查看者";
        default: return "未知";
    }
}

QString Operator::statusToString(Status status) {
    switch (status) {
        case Status::Active: return "正常";
        case Status::Inactive: return "停用";
        case Status::Locked: return "锁定";
        case Status::Expired: return "过期";
        default: return "未知";
    }
}

bool Operator::isValid() const {
    return m_status == Status::Active && isInValidPeriod();
}

bool Operator::isExpired() const {
    return QDate::currentDate() > m_validUntil;
    }

bool Operator::isInValidPeriod() const {
    QDate today = QDate::currentDate();
    return today >= m_validFrom && today <= m_validUntil;
}

QString Operator::getDisplayName() const {
    if (!m_realName.isNull() && !m_realName.isEmpty()) {
        return m_realName;
    }
    return m_username;
}

} // namespace AccessControl

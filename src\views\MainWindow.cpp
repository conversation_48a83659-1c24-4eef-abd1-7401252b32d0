#include "MainWindow.h"
#include "AutoLoginDialog.h"
#include "DepartmentManagementWidget.h"
#include "AreaManagementWidget.h"
#include "ConsumerManagementWidget.h"
#include "OperatorManagementWidget.h"
#include "../models/Operator.h"
#include "../models/Area.h"
#include "../database/dao/ControllerDao.h"
#include "../database/dao/AreaDao.h"
#include <QApplication>
#include <QCloseEvent>
#include <QResizeEvent>
#include <QScreen>
#include <QMessageBox>
#include <QDateTime>
#include <QHeaderView>
#include <QGraphicsDropShadowEffect>
#include <QPropertyAnimation>
#include <QRandomGenerator>
#include <QTableWidget>
#include <QTableWidgetItem>
#include <QDateEdit>
#include <QTimeEdit>
#include <QCheckBox>
#include <QSpinBox>
#include <QTextEdit>
#include <QGroupBox>
#include <QGridLayout>
#include <QFormLayout>
#include <QScrollArea>
#include <QToolButton>
#include <QButtonGroup>
#include <QDebug>

namespace AccessControl {

// 模块标识常量定义
const QString MainWindow::MODULE_BASIC_SETTINGS = "basic_settings";
const QString MainWindow::MODULE_CONSOLE = "console";
const QString MainWindow::MODULE_RECORD_QUERY = "record_query";
const QString MainWindow::MODULE_ATTENDANCE = "attendance";
const QString MainWindow::MODULE_ELEVATOR = "elevator";
const QString MainWindow::MODULE_PATROL = "patrol";
const QString MainWindow::MODULE_DINING = "dining";
const QString MainWindow::MODULE_MEETING = "meeting";
const QString MainWindow::MODULE_FINGERPRINT = "fingerprint";
const QString MainWindow::MODULE_FACE_RECOGNITION = "face_recognition";

MainWindow::MainWindow(const Operator& currentOperator,
                      std::shared_ptr<IDatabaseProvider> dbProvider,
                      QWidget *parent)
    : QMainWindow(parent)
    , m_currentOperator(currentOperator)
    , m_databaseProvider(dbProvider)
    , m_settings(new QSettings("AccessControl", "MainWindow", this))
    , m_centralWidget(nullptr)
    , m_mainLayout(nullptr)
    , m_titleBarFrame(nullptr)
    , m_contentFrame(nullptr)
    , m_statusBarFrame(nullptr)
    , m_navigationList(nullptr)
    , m_stackedWidget(nullptr)
    , m_basicSettingsTab(nullptr)
    // , m_consoleTab(nullptr)  // 已移除控制台标签页
    , m_attendanceTab(nullptr)
    , m_dateTimeTimer(new QTimer(this))
    , m_statusTimer(new QTimer(this))
    , m_currentModule(MODULE_BASIC_SETTINGS)
    , m_currentSubIndex(0)
    , m_isMaximized(false)
    , m_resizing(false)
    , m_dragging(false)
    , m_resizeDirection(0)
    , m_controllerTable(nullptr)
{
    // 设置窗口属性
    setWindowTitle("专业智能门禁管理系统 v1.0.0");
    setWindowIcon(QIcon(":/icons/lock.png"));
    setMinimumSize(800, 600);  // 减小最小尺寸，更实用

    // 使用无边框窗口，但我们会添加手动调整大小功能
    setWindowFlags(Qt::FramelessWindowHint);

    // 启用鼠标跟踪以支持边缘光标变化
    setMouseTracking(true);

    // 初始化界面
    initializeUI();

    // 启动定时器定期检查鼠标位置
    m_mouseTrackTimer = new QTimer(this);
    m_mouseTrackTimer->setInterval(16); // 约60FPS
    connect(m_mouseTrackTimer, &QTimer::timeout, this, [this]() {
        if (!m_resizing && !m_dragging) {
            QPoint globalMousePos = QCursor::pos();
            QPoint localMousePos = mapFromGlobal(globalMousePos);
            if (rect().contains(localMousePos)) {
                updateCursor(localMousePos);
            }
        }
    });
    m_mouseTrackTimer->start();
    initializeTitleBar();
    initializeContentArea();
    initializeStatusBar();
    initializeFunctionPages();
    initializeStyles();
    initializeConnections();
    initializeTimers();

    // 设置用户权限
    setupUserPermissions();

    // 加载窗口设置
    loadWindowSettings();

    // 默认显示基本设置模块
    switchToModule(MODULE_BASIC_SETTINGS);

    // 安装事件过滤器以确保鼠标事件能正确处理
    installEventFilters();

    qDebug() << "专业智能门禁管理系统主窗口初始化完成，当前用户:" << m_currentOperator.Operatorname();
}

MainWindow::~MainWindow() {
    saveWindowSettings();
}

void MainWindow::showMainWindow() {
    // 居中显示窗口
    QScreen* screen = QApplication::primaryScreen();
    QRect screenGeometry = screen->geometry();
    int x = (screenGeometry.width() - width()) / 2;
    int y = (screenGeometry.height() - height()) / 2;
    move(x, y);

    // 显示窗口
    show();
    raise();
    activateWindow();

    // 更新状态栏
    updateStatusBar();
}

void MainWindow::logout() {
    // 确认注销
    int ret = QMessageBox::question(this, "确认注销",
                                   "确定要注销当前用户并返回登录界面吗？",
                                   QMessageBox::Yes | QMessageBox::No,
                                   QMessageBox::No);

    if (ret == QMessageBox::Yes) {
        // 保存设置
        saveWindowSettings();

        // 隐藏主窗口
        hide();

        // 发送注销信号（父级应用程序会处理）
        QApplication::quit();
    }
}

void MainWindow::closeEvent(QCloseEvent *event) {
    // 确认退出
    int ret = QMessageBox::question(this, "确认退出",
                                   "确定要退出专业智能门禁管理系统吗？",
                                   QMessageBox::Yes | QMessageBox::No,
                                   QMessageBox::No);

    if (ret == QMessageBox::Yes) {
        saveWindowSettings();
        event->accept();
    } else {
        event->ignore();
    }
}

void MainWindow::resizeEvent(QResizeEvent *event) {
    QMainWindow::resizeEvent(event);

    // 保存窗口大小
    if (m_settings) {
        saveWindowSettings();
    }
}

// ========== 初始化方法 ==========

void MainWindow::initializeUI() {
    // 创建中央部件
    m_centralWidget = new QWidget(this);
    setCentralWidget(m_centralWidget);

    // 为中央部件也启用鼠标跟踪
    m_centralWidget->setMouseTracking(true);

    // 创建主布局（垂直布局）
    m_mainLayout = new QVBoxLayout(m_centralWidget);
    m_mainLayout->setContentsMargins(0, 0, 0, 0);
    m_mainLayout->setSpacing(0);

    // 创建三个主要区域的框架
    m_titleBarFrame = new QFrame();
    m_contentFrame = new QFrame();
    m_statusBarFrame = new QFrame();

    // 为所有框架启用鼠标跟踪
    m_titleBarFrame->setMouseTracking(true);
    m_contentFrame->setMouseTracking(true);
    m_statusBarFrame->setMouseTracking(true);

    // 设置所有框架的光标变为透明，让父窗口处理
    m_titleBarFrame->setAttribute(Qt::WA_TransparentForMouseEvents, false);
    m_contentFrame->setAttribute(Qt::WA_TransparentForMouseEvents, false);
    m_statusBarFrame->setAttribute(Qt::WA_TransparentForMouseEvents, false);

    // 设置框架样式
    m_titleBarFrame->setObjectName("titleBarFrame");
    m_contentFrame->setObjectName("contentFrame");
    m_statusBarFrame->setObjectName("statusBarFrame");

    // 设置固定高度
    m_titleBarFrame->setFixedHeight(50);
    m_statusBarFrame->setFixedHeight(30);

    // 添加到主布局
    m_mainLayout->addWidget(m_titleBarFrame);
    m_mainLayout->addWidget(m_contentFrame, 1); // 拉伸因子为1
    m_mainLayout->addWidget(m_statusBarFrame);
}

void MainWindow::initializeTitleBar() {
    // 创建标题栏布局
    m_titleBarLayout = new QHBoxLayout(m_titleBarFrame);
    m_titleBarLayout->setContentsMargins(10, 5, 10, 5);
    m_titleBarLayout->setSpacing(10);

    // === 左侧：软件图标和名称 ===
    m_appIconLabel = new QLabel();
    m_appIconLabel->setPixmap(QIcon(":/icons/lock.png").pixmap(32, 32));
    m_appIconLabel->setFixedSize(32, 32);

    m_appNameLabel = new QLabel("专业智能门禁管理系统");
    m_appNameLabel->setObjectName("appNameLabel");

    // === 右侧：汉堡菜单和窗口控制按钮 ===
    m_hamburgerButton = new QPushButton("☰");
    m_hamburgerButton->setObjectName("hamburgerButton");
    m_hamburgerButton->setFixedSize(40, 30);
    m_hamburgerButton->setToolTip("菜单");

    m_minimizeButton = new QPushButton("−");
    m_minimizeButton->setObjectName("windowControlButton");
    m_minimizeButton->setFixedSize(30, 30);
    m_minimizeButton->setToolTip("最小化");

    m_maximizeButton = new QPushButton("□");
    m_maximizeButton->setObjectName("windowControlButton");
    m_maximizeButton->setFixedSize(30, 30);
    m_maximizeButton->setToolTip("最大化/还原");

    m_closeButton = new QPushButton("✕");
    m_closeButton->setObjectName("closeButton");
    m_closeButton->setFixedSize(30, 30);
    m_closeButton->setToolTip("关闭");

    // 添加到布局
    m_titleBarLayout->addWidget(m_appIconLabel);
    m_titleBarLayout->addWidget(m_appNameLabel);
    m_titleBarLayout->addStretch(); // 弹性空间
    m_titleBarLayout->addWidget(m_hamburgerButton);
    m_titleBarLayout->addWidget(m_minimizeButton);
    m_titleBarLayout->addWidget(m_maximizeButton);
    m_titleBarLayout->addWidget(m_closeButton);

    // 创建汉堡菜单
    createHamburgerMenu();
}

void MainWindow::createHamburgerMenu() {
    m_hamburgerMenu = new QMenu(this);

    // 文件菜单
    m_fileMenu = m_hamburgerMenu->addMenu("文件");
    m_fileMenu->addAction("查看日志", this, &MainWindow::viewLogs);
    m_fileMenu->addAction("备份数据库", this, &MainWindow::backupDatabase);
    m_fileMenu->addSeparator();
    m_fileMenu->addAction("退出", this, &MainWindow::exitApplication);

    // 设置菜单
    m_settingsMenu = m_hamburgerMenu->addMenu("设置");
    m_settingsMenu->addAction("字段替换", this, &MainWindow::fieldReplacement);
    m_settingsMenu->addAction("软件定时任务", this, &MainWindow::scheduledTasks);
    m_settingsMenu->addAction("语言选择", this, &MainWindow::languageSelection);
    m_settingsMenu->addAction("皮肤", this, &MainWindow::skinSettings);
    m_settingsMenu->addAction("扩展功能", this, &MainWindow::extensionFeatures);

    // 工具菜单
    m_toolsMenu = m_hamburgerMenu->addMenu("工具");
    m_toolsMenu->addAction("操作员管理", this, &MainWindow::operatorManagement);
    m_toolsMenu->addAction("修改登录用户名及密码", this, &MainWindow::changeCredentials);
    m_toolsMenu->addAction("软件自动登录", this, &MainWindow::autoLogin);

    // 帮助菜单
    m_helpMenu = m_hamburgerMenu->addMenu("帮助");
    m_helpMenu->addAction("用户手册", this, &MainWindow::userManual);
    m_helpMenu->addAction("软件升级", this, &MainWindow::softwareUpgrade);
    m_helpMenu->addAction("问题反馈", this, &MainWindow::feedbackReport);
    m_helpMenu->addAction("关于", this, &MainWindow::aboutDialog);
}

void MainWindow::initializeContentArea() {
    // 创建内容区域布局（水平布局）
    m_contentLayout = new QHBoxLayout(m_contentFrame);
    m_contentLayout->setContentsMargins(0, 0, 0, 0);
    m_contentLayout->setSpacing(1);

    // === 左侧导航栏 ===
    m_navigationFrame = new QFrame();
    m_navigationFrame->setObjectName("navigationFrame");
    m_navigationFrame->setFixedWidth(200);

    m_navigationLayout = new QVBoxLayout(m_navigationFrame);
    m_navigationLayout->setContentsMargins(5, 10, 5, 10);
    m_navigationLayout->setSpacing(5);

    // 创建导航列表
    initializeNavigation();

    // === 右侧工作区 ===
    m_workFrame = new QFrame();
    m_workFrame->setObjectName("workFrame");

    m_workLayout = new QVBoxLayout(m_workFrame);
    m_workLayout->setContentsMargins(10, 10, 10, 10);
    m_workLayout->setSpacing(10);

    // 创建堆叠窗口用于切换不同功能页面
    m_stackedWidget = new QStackedWidget();
    m_stackedWidget->setObjectName("stackedWidget");

    m_workLayout->addWidget(m_stackedWidget);

    // 添加到内容布局
    m_contentLayout->addWidget(m_navigationFrame);
    m_contentLayout->addWidget(m_workFrame, 1); // 拉伸因子为1
}

void MainWindow::initializeNavigation() {
    // 创建导航列表
    m_navigationList = new QListWidget();
    m_navigationList->setObjectName("navigationList");

    // 添加导航项目
    createNavigationItem("基本设置", ":/icons/settings.png");
    createNavigationItem("控制台", ":/icons/console.png");
    createNavigationItem("记录查询", ":/icons/records.png");
    createNavigationItem("考勤", ":/icons/attendance.png");
    createNavigationItem("电梯管理", ":/icons/elevator.png");
    createNavigationItem("巡检", ":/icons/patrol.png");
    createNavigationItem("定额就餐", ":/icons/dining.png");
    createNavigationItem("会议签到", ":/icons/meeting.png");
    createNavigationItem("指纹管理", ":/icons/fingerprint.png");
    createNavigationItem("人脸识别", ":/icons/face.png");

    // 设置当前选中项
    m_navigationList->setCurrentRow(0);

    m_navigationLayout->addWidget(m_navigationList);
}

QListWidgetItem* MainWindow::createNavigationItem(const QString& text, const QString& icon) {
    QListWidgetItem* item = new QListWidgetItem(text);

    if (!icon.isEmpty()) {
        item->setIcon(QIcon(icon));
    }

    // 设置数据
    item->setData(Qt::UserRole, text);

    // 添加到列表
    m_navigationList->addItem(item);

    return item;
}

void MainWindow::initializeStatusBar() {
    // 创建状态栏布局
    m_statusBarLayout = new QHBoxLayout(m_statusBarFrame);
    m_statusBarLayout->setContentsMargins(10, 5, 10, 5);
    m_statusBarLayout->setSpacing(20);

    // === 左侧状态信息 ===
    m_userInfoLabel = new QLabel();
    m_userInfoLabel->setObjectName("statusLabel");
    updateUserInfo();

    m_databaseLabel = new QLabel();
    m_databaseLabel->setObjectName("statusLabel");
    updateDatabaseInfo();

    m_versionLabel = new QLabel("版本：V1.0.0");
    m_versionLabel->setObjectName("statusLabel");

    // === 右侧状态信息 ===
    m_statusLabel = new QLabel("就绪");
    m_statusLabel->setObjectName("statusLabel");

    m_countLabel = new QLabel();
    m_countLabel->setObjectName("statusLabel");
    updateCountInfo();

    m_dateTimeLabel = new QLabel();
    m_dateTimeLabel->setObjectName("statusLabel");
    updateDateTime();

    // 添加分隔符
    QLabel* separator1 = new QLabel("|");
    separator1->setObjectName("statusLabel");
    QLabel* separator2 = new QLabel("|");
    separator2->setObjectName("statusLabel");

    // 添加到布局
    m_statusBarLayout->addWidget(m_userInfoLabel);
    m_statusBarLayout->addWidget(separator1);
    m_statusBarLayout->addWidget(m_databaseLabel);
    m_statusBarLayout->addWidget(separator2);
    m_statusBarLayout->addWidget(m_versionLabel);
    m_statusBarLayout->addStretch(); // 弹性空间
    m_statusBarLayout->addWidget(m_statusLabel);
    m_statusBarLayout->addWidget(m_countLabel);
    m_statusBarLayout->addWidget(m_dateTimeLabel);
}

void MainWindow::initializeFunctionPages() {
    // 初始化各功能页面
    initializeBasicSettingsPages();
    initializeConsolePages();
    initializeRecordQueryPage();
    initializeAttendancePages();
    initializeElevatorPages();
    initializePatrolPages();
    initializeDiningPages();
    initializeMeetingPages();
    initializeFingerprintPage();
    initializeFaceRecognitionPage();
}

void MainWindow::initializeBasicSettingsPages() {
    // 创建基本设置标签页
    m_basicSettingsTab = new QTabWidget();
    m_basicSettingsTab->setObjectName("functionTab");

    // 控制器管理页面
    QWidget* controllerPage = createControllerManagementPage();
    m_basicSettingsTab->addTab(controllerPage, "控制器");

    // 部门管理页面
    QWidget* departmentPage = createDepartmentManagementPage();
    m_basicSettingsTab->addTab(departmentPage, "部门");

    // 门禁用户管理页面（区别于操作员管理）
    QWidget* accessUserPage = createAccessUserManagementPage();
    m_basicSettingsTab->addTab(accessUserPage, "用户");

    // 权限管理页面
    QWidget* permissionPage = createPermissionManagementPage();
    m_basicSettingsTab->addTab(permissionPage, "权限");

    // 时段管理页面
    QWidget* timePage = createTimeManagementPage();
    m_basicSettingsTab->addTab(timePage, "时段");

    // 密码功能页面
    QWidget* passwordPage = createPasswordManagementPage();
    m_basicSettingsTab->addTab(passwordPage, "密码功能");

    // 定时任务页面
    QWidget* taskPage = createScheduledTaskPage();
    m_basicSettingsTab->addTab(taskPage, "定时任务");

    // 添加到堆叠窗口
    m_stackedWidget->addWidget(m_basicSettingsTab);
}

// ========== 页面创建方法 ==========

QWidget* MainWindow::createControllerManagementPage() {
    QWidget* page = new QWidget();
    QVBoxLayout* layout = new QVBoxLayout(page);

    // 功能按钮栏
    QHBoxLayout* buttonLayout = new QHBoxLayout();
    buttonLayout->addWidget(new QPushButton("搜索控制器"));

    // 手动添加按钮 - 添加点击事件处理
    QPushButton* manualAddBtn = new QPushButton("手动添加");
    connect(manualAddBtn, &QPushButton::clicked, this, &MainWindow::onControllerManualAdd);
    buttonLayout->addWidget(manualAddBtn);

    buttonLayout->addWidget(new QPushButton("修改"));
    buttonLayout->addWidget(new QPushButton("删除"));
    buttonLayout->addWidget(new QPushButton("打印"));
    buttonLayout->addWidget(new QPushButton("导出Excel"));
    buttonLayout->addWidget(new QPushButton("查找"));

    // 区域管理按钮 - 添加点击事件处理
    QPushButton* areaManagementBtn = new QPushButton("区域管理");
    connect(areaManagementBtn, &QPushButton::clicked, this, [this]() {
        openAreaManagement();
    });
    buttonLayout->addWidget(areaManagementBtn);

    // 区域选择在同一行
    buttonLayout->addWidget(new QLabel("区域："));
    QComboBox* areaCombo = new QComboBox();
    areaCombo->addItem("所有区域");
    buttonLayout->addWidget(areaCombo);
    buttonLayout->addStretch();

    // 控制器列表表格
    m_controllerTable = new QTableWidget();
    m_controllerTable->setColumnCount(8);
    QStringList headers = {"控制器编号", "序列号SN", "启用", "IP", "PORT", "所在区域", "说明", "控制的门"};
    m_controllerTable->setHorizontalHeaderLabels(headers);
    m_controllerTable->horizontalHeader()->setStretchLastSection(true);

    // 设置列宽 - 控制器管理
    m_controllerTable->setColumnWidth(0, 80);  // 控制器编号
    m_controllerTable->setColumnWidth(1, 120); // 序列号SN
    m_controllerTable->setColumnWidth(2, 60);  // 启用
    m_controllerTable->setColumnWidth(3, 100); // IP
    m_controllerTable->setColumnWidth(4, 60);  // PORT
    m_controllerTable->setColumnWidth(5, 100); // 所在区域
    m_controllerTable->setColumnWidth(6, 150); // 说明
    // 最后一列"控制的门"使用拉伸

    layout->addLayout(buttonLayout);
    layout->addWidget(m_controllerTable, 1);

    // 加载控制器数据
    loadControllerData();

    return page;
}

QWidget* MainWindow::createDepartmentManagementPage() {
    // 使用完整的部门管理界面而不是简化版本
    DepartmentManagementWidget* departmentWidget = new DepartmentManagementWidget(m_databaseProvider);
    return departmentWidget;
}

QWidget* MainWindow::createAccessUserManagementPage() {
    // 使用ConsumerManagementWidget来管理门禁卡持有者
    ConsumerManagementWidget* consumerWidget = new ConsumerManagementWidget(m_databaseProvider);
    return consumerWidget;
}

QWidget* MainWindow::createPermissionManagementPage() {
    QWidget* page = new QWidget();
    QVBoxLayout* layout = new QVBoxLayout(page);

    // 功能按钮栏
    QHBoxLayout* buttonLayout = new QHBoxLayout();
    buttonLayout->addWidget(new QPushButton("批量添加"));
    buttonLayout->addWidget(new QPushButton("添加"));
    buttonLayout->addWidget(new QPushButton("修改"));
    buttonLayout->addWidget(new QPushButton("删除"));
    buttonLayout->addWidget(new QPushButton("打印"));
    buttonLayout->addWidget(new QPushButton("导出Excel"));
    buttonLayout->addWidget(new QPushButton("导入用户"));
    buttonLayout->addWidget(new QPushButton("挂失"));
    buttonLayout->addWidget(new QPushButton("查找"));
    buttonLayout->addStretch();

    // 搜索条件栏
    QHBoxLayout* searchLayout = new QHBoxLayout();
    searchLayout->addWidget(new QLabel("姓名："));
    searchLayout->addWidget(new QLineEdit());
    searchLayout->addWidget(new QLabel("卡号："));
    searchLayout->addWidget(new QLineEdit());
    searchLayout->addWidget(new QLabel("部门："));
    searchLayout->addWidget(new QComboBox());
    searchLayout->addWidget(new QPushButton("查询"));
    searchLayout->addStretch();

    // 权限列表表格
    QTableWidget* table = new QTableWidget();
    table->setColumnCount(7);
    QStringList headers = {"门", "工号", "姓名", "卡号", "部门", "时段号", "时段描述"};
    table->setHorizontalHeaderLabels(headers);
    table->horizontalHeader()->setStretchLastSection(true);

    // 设置列宽 - 权限管理
    table->setColumnWidth(0, 120); // 门
    table->setColumnWidth(1, 80);  // 工号
    table->setColumnWidth(2, 100); // 姓名
    table->setColumnWidth(3, 120); // 卡号
    table->setColumnWidth(4, 100); // 部门
    table->setColumnWidth(5, 80);  // 时段号
    // 最后一列"时段描述"使用拉伸

    layout->addLayout(buttonLayout);
    layout->addLayout(searchLayout);
    layout->addWidget(table, 1);

    return page;
}

QWidget* MainWindow::createTimeManagementPage() {
    QWidget* page = new QWidget();
    QVBoxLayout* layout = new QVBoxLayout(page);

    // 功能按钮栏
    QHBoxLayout* buttonLayout = new QHBoxLayout();
    buttonLayout->addWidget(new QPushButton("添加"));
    buttonLayout->addWidget(new QPushButton("修改"));
    buttonLayout->addWidget(new QPushButton("删除"));
    buttonLayout->addWidget(new QPushButton("打印"));
    buttonLayout->addWidget(new QPushButton("导出Excel"));
    buttonLayout->addWidget(new QPushButton("假期约束"));
    buttonLayout->addWidget(new QPushButton("查找"));
    buttonLayout->addStretch();

    // 时段列表表格
    QTableWidget* table = new QTableWidget();
    table->setColumnCount(14);
    QStringList headers = {"时段号", "星期一", "二", "三", "四", "五", "六", "日",
                          "时区1起始", "1终止", "时区2起始", "2终止", "时区3起始", "3终止"};
    table->setHorizontalHeaderLabels(headers);
    table->horizontalHeader()->setStretchLastSection(true);

    // 设置列宽 - 时段管理
    table->setColumnWidth(0, 60);  // 时段号
    table->setColumnWidth(1, 60);  // 星期一
    table->setColumnWidth(2, 40);  // 二
    table->setColumnWidth(3, 40);  // 三
    table->setColumnWidth(4, 40);  // 四
    table->setColumnWidth(5, 40);  // 五
    table->setColumnWidth(6, 40);  // 六
    table->setColumnWidth(7, 40);  // 日
    table->setColumnWidth(8, 80);  // 时区1起始
    table->setColumnWidth(9, 60);  // 1终止
    table->setColumnWidth(10, 80); // 时区2起始
    table->setColumnWidth(11, 60); // 2终止
    table->setColumnWidth(12, 80); // 时区3起始
    // 最后一列"3终止"使用拉伸

    layout->addLayout(buttonLayout);
    layout->addWidget(table, 1);

    return page;
}

QWidget* MainWindow::createPasswordManagementPage() {
    QWidget* page = new QWidget();
    QVBoxLayout* layout = new QVBoxLayout(page);

    // 添加密码区域
    QGroupBox* addGroup = new QGroupBox("添加密码");
    QFormLayout* addLayout = new QFormLayout(addGroup);

    QLineEdit* passwordEdit = new QLineEdit();
    passwordEdit->setEchoMode(QLineEdit::Password);
    addLayout->addRow("密码：", passwordEdit);

    QHBoxLayout* scopeLayout = new QHBoxLayout();
    QRadioButton* allDoorsRadio = new QRadioButton("适用于所有门");
    QRadioButton* singleDoorRadio = new QRadioButton("适用于单个门");
    allDoorsRadio->setChecked(true);
    scopeLayout->addWidget(allDoorsRadio);
    scopeLayout->addWidget(singleDoorRadio);
    scopeLayout->addStretch();
    addLayout->addRow("适用范围：", scopeLayout);

    QComboBox* doorCombo = new QComboBox();
    doorCombo->setEnabled(false);
    addLayout->addRow("选择门：", doorCombo);

    QHBoxLayout* addButtonLayout = new QHBoxLayout();
    addButtonLayout->addWidget(new QPushButton("添加超级通行密码"));
    addButtonLayout->addWidget(new QPushButton("添加普通密码"));
    addButtonLayout->addStretch();
    addLayout->addRow(addButtonLayout);

    // 密码列表区域
    QGroupBox* listGroup = new QGroupBox("密码列表");
    QVBoxLayout* listLayout = new QVBoxLayout(listGroup);

    QHBoxLayout* optionsLayout = new QHBoxLayout();
    QCheckBox* showPasswordCheck = new QCheckBox("显示密码");
    QPushButton* deleteButton = new QPushButton("删除选中");
    optionsLayout->addWidget(showPasswordCheck);
    optionsLayout->addWidget(deleteButton);
    optionsLayout->addStretch();

    QTableWidget* passwordTable = new QTableWidget();
    passwordTable->setColumnCount(4);
    QStringList headers = {"密码", "类型", "适用门", "创建时间"};
    passwordTable->setHorizontalHeaderLabels(headers);
    passwordTable->horizontalHeader()->setStretchLastSection(true);

    // 设置列宽 - 密码管理
    passwordTable->setColumnWidth(0, 120); // 密码
    passwordTable->setColumnWidth(1, 100); // 类型
    passwordTable->setColumnWidth(2, 150); // 适用门
    // 最后一列"创建时间"使用拉伸

    listLayout->addLayout(optionsLayout);
    listLayout->addWidget(passwordTable);

    layout->addWidget(addGroup);
    layout->addWidget(listGroup, 1);

    return page;
}

QWidget* MainWindow::createScheduledTaskPage() {
    QWidget* page = new QWidget();
    QVBoxLayout* layout = new QVBoxLayout(page);

    // 功能按钮栏
    QHBoxLayout* buttonLayout = new QHBoxLayout();
    buttonLayout->addWidget(new QPushButton("添加任务"));
    buttonLayout->addWidget(new QPushButton("修改任务"));
    buttonLayout->addWidget(new QPushButton("删除任务"));
    buttonLayout->addWidget(new QPushButton("启用/禁用"));
    buttonLayout->addStretch();

    // 任务列表表格
    QTableWidget* table = new QTableWidget();
    table->setColumnCount(8);
    QStringList headers = {"任务编号", "起始日期", "截止日期", "动作时间", "星期选项", "适用于门", "控制方式", "备注"};
    table->setHorizontalHeaderLabels(headers);
    table->horizontalHeader()->setStretchLastSection(true);

    // 设置列宽 - 定时任务
    table->setColumnWidth(0, 80);  // 任务编号
    table->setColumnWidth(1, 100); // 起始日期
    table->setColumnWidth(2, 100); // 截止日期
    table->setColumnWidth(3, 100); // 动作时间
    table->setColumnWidth(4, 100); // 星期选项
    table->setColumnWidth(5, 120); // 适用于门
    table->setColumnWidth(6, 100); // 控制方式
    // 最后一列"备注"使用拉伸

    layout->addLayout(buttonLayout);
    layout->addWidget(table, 1);

    return page;
}

QWidget* MainWindow::createAreaManagementPage() {
    AreaManagementWidget* areaWidget = new AreaManagementWidget(m_databaseProvider, this);
    return areaWidget;
}

void MainWindow::initializeConsolePages() {
    // 直接使用实时监控页面，不需要标签页
    QWidget* monitorPage = createRealTimeMonitorPage();

    // 直接添加到堆叠窗口，不使用标签页
    m_stackedWidget->addWidget(monitorPage);
}

QWidget* MainWindow::createRealTimeMonitorPage() {
    QWidget* page = new QWidget();
    QVBoxLayout* layout = new QVBoxLayout(page);

    // 控制按钮栏
    QHBoxLayout* buttonLayout = new QHBoxLayout();
    buttonLayout->addWidget(new QPushButton("实时监控"));
    buttonLayout->addWidget(new QPushButton("停止"));
    buttonLayout->addWidget(new QPushButton("检测控制器"));
    buttonLayout->addWidget(new QPushButton("校准时间"));
    buttonLayout->addWidget(new QPushButton("上传设置"));
    buttonLayout->addWidget(new QPushButton("提取记录"));
    buttonLayout->addWidget(new QPushButton("实时提取"));
    buttonLayout->addWidget(new QPushButton("远程开门"));
    buttonLayout->addWidget(new QPushButton("电子地图"));
    buttonLayout->addWidget(new QPushButton("门内人员"));
    buttonLayout->addWidget(new QPushButton("一键常开"));
    buttonLayout->addWidget(new QPushButton("一键常闭"));

    QComboBox* areaCombo = new QComboBox();
    areaCombo->addItem("所有区域");
    buttonLayout->addWidget(areaCombo);
    buttonLayout->addStretch();

    // 监控显示区域
    QTextEdit* monitorDisplay = new QTextEdit();
    monitorDisplay->setReadOnly(true);
    monitorDisplay->setStyleSheet("background-color: #000; color: #00ff00; font-family: 'Courier New';");

    layout->addLayout(buttonLayout);
    layout->addWidget(monitorDisplay, 1);

    return page;
}

void MainWindow::initializeRecordQueryPage() {
    QWidget* page = new QWidget();
    QVBoxLayout* layout = new QVBoxLayout(page);

    // 搜索条件区域
    QGroupBox* searchGroup = new QGroupBox("查询条件");
    QGridLayout* searchLayout = new QGridLayout(searchGroup);

    searchLayout->addWidget(new QLabel("时间区间："), 0, 0);
    searchLayout->addWidget(new QDateTimeEdit(QDateTime::currentDateTime().addDays(-7)), 0, 1);
    searchLayout->addWidget(new QLabel("至"), 0, 2);
    searchLayout->addWidget(new QDateTimeEdit(QDateTime::currentDateTime()), 0, 3);

    searchLayout->addWidget(new QLabel("地点："), 1, 0);
    searchLayout->addWidget(new QComboBox(), 1, 1);
    searchLayout->addWidget(new QLabel("姓名："), 1, 2);
    searchLayout->addWidget(new QLineEdit(), 1, 3);

    searchLayout->addWidget(new QLabel("卡号："), 2, 0);
    searchLayout->addWidget(new QLineEdit(), 2, 1);
    searchLayout->addWidget(new QLabel("部门："), 2, 2);
    searchLayout->addWidget(new QComboBox(), 2, 3);

    searchLayout->addWidget(new QLabel("事件："), 3, 0);
    searchLayout->addWidget(new QComboBox(), 3, 1);

    QHBoxLayout* searchButtonLayout = new QHBoxLayout();
    searchButtonLayout->addWidget(new QPushButton("查询"));
    searchButtonLayout->addWidget(new QPushButton("清空条件"));
    searchButtonLayout->addWidget(new QPushButton("打印"));
    searchButtonLayout->addWidget(new QPushButton("导出到Excel"));
    searchButtonLayout->addStretch();
    searchLayout->addLayout(searchButtonLayout, 4, 0, 1, 4);

    // 记录列表表格
    QTableWidget* table = new QTableWidget();
    table->setColumnCount(9);
    QStringList headers = {"序号", "卡号", "工号", "姓名", "部门", "时间", "地点", "状态", "描述"};
    table->setHorizontalHeaderLabels(headers);
    table->horizontalHeader()->setStretchLastSection(true);

    // 设置列宽 - 记录查询
    table->setColumnWidth(0, 60);  // 序号
    table->setColumnWidth(1, 120); // 卡号
    table->setColumnWidth(2, 80);  // 工号
    table->setColumnWidth(3, 100); // 姓名
    table->setColumnWidth(4, 100); // 部门
    table->setColumnWidth(5, 150); // 时间
    table->setColumnWidth(6, 100); // 地点
    table->setColumnWidth(7, 80);  // 状态
    // 最后一列"描述"使用拉伸

    layout->addWidget(searchGroup);
    layout->addWidget(table, 1);

    // 添加到堆叠窗口
    m_stackedWidget->addWidget(page);
}

void MainWindow::initializeAttendancePages() {
    // 其他功能页面暂时使用简单实现
    QWidget* page = new QWidget();
    QVBoxLayout* layout = new QVBoxLayout(page);
    layout->addWidget(new QLabel("考勤功能 - 开发中"));
    m_stackedWidget->addWidget(page);
}

void MainWindow::initializeElevatorPages() {
    QWidget* page = new QWidget();
    QVBoxLayout* layout = new QVBoxLayout(page);
    layout->addWidget(new QLabel("电梯管理功能 - 开发中"));
    m_stackedWidget->addWidget(page);
}

void MainWindow::initializePatrolPages() {
    QWidget* page = new QWidget();
    QVBoxLayout* layout = new QVBoxLayout(page);
    layout->addWidget(new QLabel("巡检功能 - 开发中"));
    m_stackedWidget->addWidget(page);
}

void MainWindow::initializeDiningPages() {
    QWidget* page = new QWidget();
    QVBoxLayout* layout = new QVBoxLayout(page);
    layout->addWidget(new QLabel("定额就餐功能 - 开发中"));
    m_stackedWidget->addWidget(page);
}

void MainWindow::initializeMeetingPages() {
    QWidget* page = new QWidget();
    QVBoxLayout* layout = new QVBoxLayout(page);
    layout->addWidget(new QLabel("会议签到功能 - 开发中"));
    m_stackedWidget->addWidget(page);
}

void MainWindow::initializeFingerprintPage() {
    QWidget* page = new QWidget();
    QVBoxLayout* layout = new QVBoxLayout(page);
    layout->addWidget(new QLabel("指纹管理功能 - 开发中"));
    m_stackedWidget->addWidget(page);
}

void MainWindow::initializeFaceRecognitionPage() {
    QWidget* page = new QWidget();
    QVBoxLayout* layout = new QVBoxLayout(page);
    layout->addWidget(new QLabel("人脸识别功能 - 开发中"));
    m_stackedWidget->addWidget(page);
}

void MainWindow::initializeStyles() {
    // 设置整体样式
    setStyleSheet(R"(
        /* 标题栏样式 */
        #titleBarFrame {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                      stop:0 #2196F3, stop:1 #1976D2);
            border-bottom: 1px solid #1565C0;
        }

        #appNameLabel {
            color: white;
            font-size: 16px;
            font-weight: bold;
        }

        #hamburgerButton {
            background-color: rgba(255, 255, 255, 0.1);
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 16px;
            font-weight: bold;
        }

        #hamburgerButton:hover {
            background-color: rgba(255, 255, 255, 0.2);
        }

        #windowControlButton {
            background-color: rgba(255, 255, 255, 0.1);
            color: white;
            border: none;
            border-radius: 4px;
        }

        #windowControlButton:hover {
            background-color: rgba(255, 255, 255, 0.2);
        }

        #closeButton {
            background-color: rgba(255, 255, 255, 0.1);
            color: white;
            border: none;
            border-radius: 4px;
        }

        #closeButton:hover {
            background-color: #F44336;
        }

        /* 导航栏样式 */
        #navigationFrame {
            background-color: #f8f9fa;
            border-right: 1px solid #dee2e6;
        }

        #navigationList {
            background-color: transparent;
            border: none;
            outline: none;
        }

        #navigationList::item {
            height: 45px;
            border-radius: 6px;
            margin: 2px 0;
            padding: 0 10px;
            color: #495057;
            font-size: 14px;
        }

        #navigationList::item:hover {
            background-color: #e9ecef;
        }

        #navigationList::item:selected {
            background-color: #2196F3;
            color: white;
            font-weight: bold;
        }

        /* 工作区样式 */
        #workFrame {
            background-color: white;
        }

        #stackedWidget {
            background-color: white;
            border: none;
        }

        #functionTab {
            background-color: white;
        }

        #functionTab::pane {
            border: 1px solid #dee2e6;
            border-radius: 4px;
            background-color: white;
        }

        #functionTab::tab-bar {
            alignment: left;
        }

        #functionTab QTabBar::tab {
            background-color: #f8f9fa;
            color: #495057;
            border: 1px solid #dee2e6;
            border-bottom: none;
            padding: 8px 16px;
            margin-right: 2px;
            border-top-left-radius: 4px;
            border-top-right-radius: 4px;
        }

        #functionTab QTabBar::tab:hover {
            background-color: #e9ecef;
        }

        #functionTab QTabBar::tab:selected {
            background-color: white;
            color: #2196F3;
            font-weight: bold;
        }

        /* 状态栏样式 */
        #statusBarFrame {
            background-color: #f8f9fa;
            border-top: 1px solid #dee2e6;
        }

        #statusLabel {
            color: #495057;
            font-size: 12px;
            padding: 0 5px;
        }

        /* 表格样式 */
        QTableWidget {
            gridline-color: #dee2e6;
            background-color: white;
            alternate-background-color: #f8f9fa;
            selection-background-color: #2196F3;
            border: 1px solid #dee2e6;
            border-radius: 4px;
        }

        QTableWidget::item {
            padding: 8px;
            border: none;
        }

        QTableWidget::item:selected {
            background-color: #2196F3;
            color: white;
        }

        QHeaderView::section {
            background-color: #e9ecef;
            color: #495057;
            padding: 8px;
            border: none;
            border-right: 1px solid #dee2e6;
            font-weight: bold;
        }

        /* 按钮样式 */
        QPushButton {
            background-color: #2196F3;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 4px;
            font-size: 12px;
        }

        QPushButton:hover {
            background-color: #1976D2;
        }

        QPushButton:pressed {
            background-color: #1565C0;
        }

        /* 输入框样式 */
        QLineEdit, QComboBox, QSpinBox, QDateTimeEdit {
            border: 1px solid #ced4da;
            border-radius: 4px;
            padding: 6px 8px;
            font-size: 12px;
        }

        QLineEdit:focus, QComboBox:focus, QSpinBox:focus, QDateTimeEdit:focus {
            border-color: #2196F3;
            outline: none;
        }

        /* 分组框样式 */
        QGroupBox {
            font-weight: bold;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            margin-top: 10px;
            padding-top: 10px;
        }

        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
            color: #495057;
        }
    )");
}

void MainWindow::initializeConnections() {
    // 导航列表点击事件
    connect(m_navigationList, &QListWidget::itemClicked,
            this, &MainWindow::onNavigationItemClicked);

    // 标签页切换事件
    if (m_basicSettingsTab) {
        connect(m_basicSettingsTab, &QTabWidget::currentChanged,
                this, &MainWindow::onSubTabChanged);
    }

    // 汉堡菜单按钮
    connect(m_hamburgerButton, &QPushButton::clicked,
            this, &MainWindow::onHamburgerMenuClicked);

    // 窗口控制按钮
    connect(m_minimizeButton, &QPushButton::clicked,
            this, &MainWindow::minimizeWindow);
    connect(m_maximizeButton, &QPushButton::clicked,
            this, &MainWindow::maximizeWindow);
    connect(m_closeButton, &QPushButton::clicked,
            this, &MainWindow::closeWindow);
}

void MainWindow::initializeTimers() {
    // 日期时间更新定时器
    connect(m_dateTimeTimer, &QTimer::timeout, this, &MainWindow::updateDateTime);
    m_dateTimeTimer->start(1000); // 每秒更新一次

    // 状态更新定时器
    connect(m_statusTimer, &QTimer::timeout, this, &MainWindow::updateStatusBar);
    m_statusTimer->start(30000); // 每30秒更新一次状态
}

void MainWindow::setupUserPermissions() {
    // 根据用户权限设置界面可见性
    // 这里暂时不做具体实现，后续可以根据需要禁用某些功能
}

// ========== 事件处理方法 ==========

void MainWindow::onNavigationItemClicked(QListWidgetItem *item) {
    if (!item) return;

    QString moduleName = item->data(Qt::UserRole).toString();
    switchToModule(moduleName);
}

void MainWindow::switchToModule(const QString& moduleName) {
    if (moduleName == m_currentModule) {
        return; // 已经在当前模块
    }

    m_currentModule = moduleName;

    // 根据模块名切换到对应页面
    int pageIndex = 0;
    if (moduleName == "基本设置") {
        pageIndex = 0;
        m_statusLabel->setText("基本设置");
    } else if (moduleName == "控制台") {
        pageIndex = 1;
        m_statusLabel->setText("控制台");
    } else if (moduleName == "记录查询") {
        pageIndex = 2;
        m_statusLabel->setText("记录查询");
    } else if (moduleName == "考勤") {
        pageIndex = 3;
        m_statusLabel->setText("考勤管理");
    } else if (moduleName == "电梯管理") {
        pageIndex = 4;
        m_statusLabel->setText("电梯管理");
    } else if (moduleName == "巡检") {
        pageIndex = 5;
        m_statusLabel->setText("巡检管理");
    } else if (moduleName == "定额就餐") {
        pageIndex = 6;
        m_statusLabel->setText("定额就餐");
    } else if (moduleName == "会议签到") {
        pageIndex = 7;
        m_statusLabel->setText("会议签到");
    } else if (moduleName == "指纹管理") {
        pageIndex = 8;
        m_statusLabel->setText("指纹管理");
    } else if (moduleName == "人脸识别") {
        pageIndex = 9;
        m_statusLabel->setText("人脸识别");
    }

    m_stackedWidget->setCurrentIndex(pageIndex);
    updateCountInfo();
}

void MainWindow::onSubTabChanged(int index) {
    m_currentSubIndex = index;
    updateCountInfo();
}

void MainWindow::onHamburgerMenuClicked() {
    // 显示汉堡菜单
    QPoint pos = m_hamburgerButton->mapToGlobal(QPoint(0, m_hamburgerButton->height()));
    m_hamburgerMenu->exec(pos);
}

// ========== 菜单显示方法 ==========

void MainWindow::showFileMenu() {
    // 显示文件菜单 - 这个方法在创建汉堡菜单时已经通过addMenu方式处理了
    // 如果需要单独显示文件菜单，可以在这里实现
    QPoint pos = m_hamburgerButton->mapToGlobal(QPoint(0, m_hamburgerButton->height()));
    m_fileMenu->exec(pos);
}

void MainWindow::showSettingsMenu() {
    // 显示设置菜单
    QPoint pos = m_hamburgerButton->mapToGlobal(QPoint(0, m_hamburgerButton->height()));
    m_settingsMenu->exec(pos);
}

void MainWindow::showToolsMenu() {
    // 显示工具菜单
    QPoint pos = m_hamburgerButton->mapToGlobal(QPoint(0, m_hamburgerButton->height()));
    m_toolsMenu->exec(pos);
}

void MainWindow::showHelpMenu() {
    // 显示帮助菜单
    QPoint pos = m_hamburgerButton->mapToGlobal(QPoint(0, m_hamburgerButton->height()));
    m_helpMenu->exec(pos);
}

// ========== 菜单事件处理 ==========

void MainWindow::viewLogs() {
    QMessageBox::information(this, "查看日志", "日志查看功能正在开发中...");
}

void MainWindow::backupDatabase() {
    QMessageBox::information(this, "备份数据库", "数据库备份功能正在开发中...");
}

void MainWindow::exitApplication() {
    close();
}

void MainWindow::fieldReplacement() {
    QMessageBox::information(this, "字段替换", "字段替换功能正在开发中...");
}

void MainWindow::scheduledTasks() {
    QMessageBox::information(this, "软件定时任务", "软件定时任务功能正在开发中...");
}

void MainWindow::languageSelection() {
    QMessageBox::information(this, "语言选择", "语言选择功能正在开发中...");
}

void MainWindow::skinSettings() {
    QMessageBox::information(this, "皮肤设置", "皮肤设置功能正在开发中...");
}

void MainWindow::extensionFeatures() {
    QMessageBox::information(this, "扩展功能", "扩展功能正在开发中...");
}

void MainWindow::operatorManagement() {
    // 创建独立窗口显示操作员管理（软件登录用户管理）
    QDialog* operatorDialog = new QDialog(this);
    operatorDialog->setWindowTitle("操作员管理");
    operatorDialog->setModal(false);
    operatorDialog->resize(1000, 700);

    QVBoxLayout* layout = new QVBoxLayout(operatorDialog);
    layout->setContentsMargins(10, 10, 10, 10);

    // 添加说明标签
    QLabel* infoLabel = new QLabel("操作员管理 - 管理软件登录用户及其操作权限");
    infoLabel->setStyleSheet("font-weight: bold; color: #2196F3; padding: 5px;");
    layout->addWidget(infoLabel);

    // 创建OperatorManagementWidget实例，用于管理软件登录用户
    OperatorManagementWidget* operatorWidget = new OperatorManagementWidget(m_databaseProvider, operatorDialog);
    layout->addWidget(operatorWidget);

    operatorDialog->show();
}

void MainWindow::changeCredentials() {
    QMessageBox::information(this, "修改用户名密码", "修改用户名密码功能正在开发中...");
}

void MainWindow::autoLogin() {
    // 创建并显示自动登录设置对话框
    AutoLoginDialog* dialog = new AutoLoginDialog(this);

    // 居中显示对话框
    QRect parentGeometry = this->geometry();
    int x = parentGeometry.x() + (parentGeometry.width() - dialog->width()) / 2;
    int y = parentGeometry.y() + (parentGeometry.height() - dialog->height()) / 2;
    dialog->move(x, y);

    // 显示对话框
    int result = dialog->exec();

    if (result == QDialog::Accepted) {
        // 用户点击了应用按钮，设置已保存
        updateStatusBar();
        m_statusLabel->setText("自动登录设置已更新");

        // 3秒后恢复状态栏
        QTimer::singleShot(3000, this, [this]() {
            m_statusLabel->setText("就绪");
        });
    }

    // 清理对话框
    dialog->deleteLater();
}

void MainWindow::openAreaManagement() {
    // 创建独立窗口显示区域管理
    AreaManagementWidget* areaDialog = new AreaManagementWidget(m_databaseProvider, this);
    areaDialog->setWindowTitle("区域管理");
    areaDialog->setWindowFlags(Qt::Window);  // 设置为独立窗口
    areaDialog->resize(1200, 700);
    areaDialog->setAttribute(Qt::WA_DeleteOnClose);  // 窗口关闭时自动清理

    // 居中显示
    QRect parentGeometry = this->geometry();
    int x = parentGeometry.x() + (parentGeometry.width() - areaDialog->width()) / 2;
    int y = parentGeometry.y() + (parentGeometry.height() - areaDialog->height()) / 2;
    areaDialog->move(x, y);

    // 显示独立窗口
    areaDialog->show();

    // 更新状态栏
    updateStatusBar();
    m_statusLabel->setText("区域管理已打开");

    // 3秒后恢复状态栏
    QTimer::singleShot(3000, this, [this]() {
        m_statusLabel->setText("就绪");
    });
}

void MainWindow::userManual() {
    QMessageBox::information(this, "用户手册", "用户手册功能正在开发中...");
}

void MainWindow::softwareUpgrade() {
    QMessageBox::information(this, "软件升级", "软件升级功能正在开发中...");
}

void MainWindow::feedbackReport() {
    QMessageBox::information(this, "问题反馈", "问题反馈功能正在开发中...");
}

void MainWindow::aboutDialog() {
    QMessageBox::about(this, "关于专业智能门禁管理系统",
                      "专业智能门禁管理系统 v1.0.0\n\n"
                      "一个功能完整的门禁管理解决方案\n"
                      "支持多种数据库和现代化界面\n"
                      "包含考勤、巡检、电梯管理等专业功能\n\n"
                      "版权所有 © 2025");
}

// ========== 窗口控制事件处理 ==========

void MainWindow::minimizeWindow() {
    showMinimized();
}

void MainWindow::maximizeWindow() {
    if (m_isMaximized) {
        showNormal();
        m_maximizeButton->setText("□");
        m_isMaximized = false;
    } else {
        showMaximized();
        m_maximizeButton->setText("❐");
        m_isMaximized = true;
    }
}

void MainWindow::closeWindow() {
    close();
}

// ========== 状态更新方法 ==========

void MainWindow::updateStatusBar() {
    // 更新状态栏信息
    updateUserInfo();
    updateDatabaseInfo();
    updateCountInfo();
}

void MainWindow::updateUserInfo() {
    if (m_userInfoLabel) {
        QString userInfo = QString("管理员：%1").arg(m_currentOperator.Operatorname());
        m_userInfoLabel->setText(userInfo);
    }
}

void MainWindow::updateDatabaseInfo() {
    if (m_databaseLabel) {
        QString dbType = "SQLite";
        if (m_databaseProvider) {
            dbType = m_databaseProvider->databaseType();
        }
        m_databaseLabel->setText(QString("数据库：%1").arg(dbType));
    }
}

void MainWindow::updateCountInfo() {
    if (m_countLabel) {
        QString countText = "数量：0";

        // 根据当前模块显示不同的计数
        if (m_currentModule == "基本设置") {
            if (m_currentSubIndex == 0) {
                countText = "控制器数量：0";
            } else if (m_currentSubIndex == 1) {
                countText = "部门数量：0";
            } else if (m_currentSubIndex == 2) {
                // 获取用户数量
                if (m_databaseProvider) {
                    // 这里可以调用UserDao获取实际数量
                    countText = "用户数量：4"; // 暂时硬编码
                }
            }
        } else if (m_currentModule == "记录查询") {
            countText = "记录数量：0";
        } else if (m_currentModule == "考勤") {
            countText = "考勤记录：0";
        }

        m_countLabel->setText(countText);
    }
}

void MainWindow::updateDateTime() {
    if (m_dateTimeLabel) {
        QDateTime currentDateTime = QDateTime::currentDateTime();
        QString timeText = currentDateTime.toString("yyyy-MM-dd hh:mm:ss");

        // 添加星期
        QString weekDay;
        int dayOfWeek = currentDateTime.date().dayOfWeek();
        switch (dayOfWeek) {
            case 1: weekDay = "星期一"; break;
            case 2: weekDay = "星期二"; break;
            case 3: weekDay = "星期三"; break;
            case 4: weekDay = "星期四"; break;
            case 5: weekDay = "星期五"; break;
            case 6: weekDay = "星期六"; break;
            case 7: weekDay = "星期日"; break;
        }

        timeText += " " + weekDay;
        m_dateTimeLabel->setText(timeText);
    }
}

// ========== 设置保存加载 ==========

void MainWindow::loadWindowSettings() {
    // 加载窗口几何
    QByteArray geometry = m_settings->value("geometry").toByteArray();
    if (!geometry.isEmpty()) {
        restoreGeometry(geometry);
    } else {
        // 默认大小和位置
        resize(1400, 900);

        // 居中显示
        QScreen* screen = QApplication::primaryScreen();
        QRect screenGeometry = screen->geometry();
        int x = (screenGeometry.width() - width()) / 2;
        int y = (screenGeometry.height() - height()) / 2;
        move(x, y);
    }

    // 加载最后使用的模块
    QString lastModule = m_settings->value("lastModule", "基本设置").toString();
    switchToModule(lastModule);
}

void MainWindow::saveWindowSettings() {
    if (!m_settings) return;

    // 保存窗口几何
    m_settings->setValue("geometry", saveGeometry());

    // 保存当前模块
    m_settings->setValue("lastModule", m_currentModule);

    // 保存当前子标签索引
    m_settings->setValue("currentSubIndex", m_currentSubIndex);
}

// ========== 鼠标事件处理（支持窗口调整大小） ==========

void MainWindow::mousePressEvent(QMouseEvent *event) {
    if (event->button() == Qt::LeftButton) {
        m_lastMousePos = event->globalPosition().toPoint();
        m_dragStartPos = event->pos();

        // 检查是否在窗口边缘（用于调整大小）
        // 最大化状态下不允许调整大小
        if (!isMaximized()) {
            QRect rect = this->rect();
            QPoint pos = event->pos();

            const int margin = 10; // 边缘检测范围，与updateCursor保持一致
            m_resizeDirection = 0;

            if (pos.x() <= margin) m_resizeDirection |= 1; // 左边
            if (pos.x() >= rect.width() - margin) m_resizeDirection |= 2; // 右边
            if (pos.y() <= margin) m_resizeDirection |= 4; // 上边
            if (pos.y() >= rect.height() - margin) m_resizeDirection |= 8; // 下边

            if (m_resizeDirection != 0) {
                m_resizing = true;
                // 阻止事件传播到子组件
                event->accept();
                return;
            }
        }

        // 检查是否在标题栏区域（用于拖动窗口）
        QPoint pos = event->pos();
        if (m_titleBarFrame && m_titleBarFrame->geometry().contains(pos)) {
            // 排除按钮区域
            QPoint titleBarPos = pos - m_titleBarFrame->geometry().topLeft();
            bool inButtonArea = false;

            // 检查是否点击在按钮上
            if (m_hamburgerButton && m_hamburgerButton->geometry().contains(titleBarPos)) {
                inButtonArea = true;
            }
            if (m_minimizeButton && m_minimizeButton->geometry().contains(titleBarPos)) {
                inButtonArea = true;
            }
            if (m_maximizeButton && m_maximizeButton->geometry().contains(titleBarPos)) {
                inButtonArea = true;
            }
            if (m_closeButton && m_closeButton->geometry().contains(titleBarPos)) {
                inButtonArea = true;
            }

            if (!inButtonArea) {
                m_dragging = true;
                // 阻止事件传播到子组件
                event->accept();
                return;
            }
        }
    }

    QMainWindow::mousePressEvent(event);
}

void MainWindow::mouseMoveEvent(QMouseEvent *event) {
    // 处理鼠标按下时的窗口调整大小和拖动
    if (event->buttons() & Qt::LeftButton) {
        QPoint currentGlobalPos = event->globalPosition().toPoint();
        QPoint delta = currentGlobalPos - m_lastMousePos;

        if (m_resizing) {
            // 窗口大小调整
            QRect newGeometry = geometry();

            // 根据调整方向修改窗口几何
            if (m_resizeDirection & 1) { // 左边
                newGeometry.setLeft(newGeometry.left() + delta.x());
            }
            if (m_resizeDirection & 2) { // 右边
                newGeometry.setRight(newGeometry.right() + delta.x());
            }
            if (m_resizeDirection & 4) { // 上边
                newGeometry.setTop(newGeometry.top() + delta.y());
            }
            if (m_resizeDirection & 8) { // 下边
                newGeometry.setBottom(newGeometry.bottom() + delta.y());
            }

            // 确保不小于最小尺寸
            if (newGeometry.width() >= minimumWidth() &&
                newGeometry.height() >= minimumHeight()) {
                setGeometry(newGeometry);
            }

            m_lastMousePos = currentGlobalPos;
            event->accept();
            return;
        } else if (m_dragging) {
            // 窗口拖动
            QPoint newPos = pos() + delta;
            move(newPos);
            m_lastMousePos = currentGlobalPos;
            event->accept();
            return;
        }
    }

    // 总是更新光标，无论鼠标是否按下
    updateCursor(event->pos());

    QMainWindow::mouseMoveEvent(event);
}

void MainWindow::mouseReleaseEvent(QMouseEvent *event) {
    if (event->button() == Qt::LeftButton) {
        m_resizing = false;
        m_dragging = false;
        m_resizeDirection = 0;
        setCursor(Qt::ArrowCursor);
    }

    QMainWindow::mouseReleaseEvent(event);
}

void MainWindow::mouseDoubleClickEvent(QMouseEvent *event) {
    if (event->button() == Qt::LeftButton) {
        // 检查是否在标题栏区域双击
        if (m_titleBarFrame && m_titleBarFrame->geometry().contains(event->pos())) {
            // 排除按钮区域
            QPoint titleBarPos = event->pos() - m_titleBarFrame->geometry().topLeft();
            bool inButtonArea = false;

            // 检查是否双击在按钮上
            if (m_hamburgerButton && m_hamburgerButton->geometry().contains(titleBarPos)) {
                inButtonArea = true;
            }
            if (m_minimizeButton && m_minimizeButton->geometry().contains(titleBarPos)) {
                inButtonArea = true;
            }
            if (m_maximizeButton && m_maximizeButton->geometry().contains(titleBarPos)) {
                inButtonArea = true;
            }
            if (m_closeButton && m_closeButton->geometry().contains(titleBarPos)) {
                inButtonArea = true;
            }

            if (!inButtonArea) {
                // 切换最大化/还原状态
                maximizeWindow();
            }
        }
    }

    QMainWindow::mouseDoubleClickEvent(event);
}

void MainWindow::enterEvent(QEnterEvent *event) {
    // qDebug() << "Mouse entered main window";
    QMainWindow::enterEvent(event);
}

void MainWindow::leaveEvent(QEvent *event) {
    // qDebug() << "Mouse left main window";
    setCursor(Qt::ArrowCursor);  // 离开时重置光标
    QMainWindow::leaveEvent(event);
}

void MainWindow::updateCursor(const QPoint& pos) {
    // 仅在非拖拽状态时更新光标
    if (m_resizing || m_dragging) {
        return;
    }

    QRect rect = this->rect();
    const int margin = 10;  // 增大边缘检测区域，使其更容易触发
    Qt::CursorShape cursor = Qt::ArrowCursor;

    // 调试输出 - 可以注释掉
    // qDebug() << "updateCursor: pos=" << pos << "rect=" << rect << "margin=" << margin;

    // 最大化状态下不显示调整大小光标
    if (!isMaximized()) {
        bool left = pos.x() <= margin;
        bool right = pos.x() >= rect.width() - margin;
        bool top = pos.y() <= margin;
        bool bottom = pos.y() >= rect.height() - margin;

        // 调试输出 - 可以注释掉
        // qDebug() << "Edge detection: left=" << left << "right=" << right << "top=" << top << "bottom=" << bottom;

        // 优先检查角落（对角线调整）
        if ((left && top) || (right && bottom)) {
            cursor = Qt::SizeFDiagCursor;
            // qDebug() << "Setting cursor to SizeFDiagCursor";
        } else if ((right && top) || (left && bottom)) {
            cursor = Qt::SizeBDiagCursor;
            // qDebug() << "Setting cursor to SizeBDiagCursor";
        }
        // 然后检查边缘（水平/垂直调整）
        else if (left || right) {
            cursor = Qt::SizeHorCursor;
            // qDebug() << "Setting cursor to SizeHorCursor";
        } else if (top || bottom) {
            cursor = Qt::SizeVerCursor;
            // qDebug() << "Setting cursor to SizeVerCursor";
        }
        // 最后检查标题栏拖动区域
        else if (m_titleBarFrame && m_titleBarFrame->geometry().contains(pos)) {
            // 排除按钮区域
            QPoint titleBarPos = pos - m_titleBarFrame->geometry().topLeft();
            bool inButtonArea = false;

            if (m_hamburgerButton && m_hamburgerButton->geometry().contains(titleBarPos)) {
                inButtonArea = true;
            }
            if (m_minimizeButton && m_minimizeButton->geometry().contains(titleBarPos)) {
                inButtonArea = true;
            }
            if (m_maximizeButton && m_maximizeButton->geometry().contains(titleBarPos)) {
                inButtonArea = true;
            }
            if (m_closeButton && m_closeButton->geometry().contains(titleBarPos)) {
                inButtonArea = true;
            }

            if (!inButtonArea) {
                cursor = Qt::SizeAllCursor;  // 标题栏拖动光标
            }
        }
    } else if (m_titleBarFrame && m_titleBarFrame->geometry().contains(pos)) {
        // 最大化时只在标题栏显示移动光标
        QPoint titleBarPos = pos - m_titleBarFrame->geometry().topLeft();
        bool inButtonArea = false;

        if (m_hamburgerButton && m_hamburgerButton->geometry().contains(titleBarPos)) {
            inButtonArea = true;
        }
        if (m_minimizeButton && m_minimizeButton->geometry().contains(titleBarPos)) {
            inButtonArea = true;
        }
        if (m_maximizeButton && m_maximizeButton->geometry().contains(titleBarPos)) {
            inButtonArea = true;
        }
        if (m_closeButton && m_closeButton->geometry().contains(titleBarPos)) {
            inButtonArea = true;
        }

        if (!inButtonArea) {
            cursor = Qt::SizeAllCursor;
        }
    }

    setCursor(cursor);
}

bool MainWindow::eventFilter(QObject *watched, QEvent *event) {
    // 只处理鼠标相关事件
    if (event->type() == QEvent::MouseMove ||
        event->type() == QEvent::MouseButtonPress ||
        event->type() == QEvent::MouseButtonRelease) {

        QMouseEvent *mouseEvent = static_cast<QMouseEvent*>(event);

        // 将子组件的鼠标位置转换为主窗口的坐标
        QPoint globalPos = mouseEvent->globalPosition().toPoint();
        QPoint localPos = mapFromGlobal(globalPos);

        if (event->type() == QEvent::MouseMove) {
            // 鼠标移动事件：更新光标形状
            if (!m_resizing && !m_dragging) {
                updateCursor(localPos);
            }
        }
        else if (event->type() == QEvent::MouseButtonPress && mouseEvent->button() == Qt::LeftButton) {
            // 鼠标按下事件：检查是否在边缘，如果是，转发到主窗口处理
            if (!isMaximized()) {
                QRect rect = this->rect();
                const int margin = 10;

                bool left = localPos.x() <= margin;
                bool right = localPos.x() >= rect.width() - margin;
                bool top = localPos.y() <= margin;
                bool bottom = localPos.y() >= rect.height() - margin;

                // 如果在边缘，创建新的鼠标事件转发给主窗口
                if (left || right || top || bottom) {
                    QMouseEvent newEvent(QEvent::MouseButtonPress, localPos, globalPos,
                                       mouseEvent->button(), mouseEvent->buttons(),
                                       mouseEvent->modifiers());
                    mousePressEvent(&newEvent);
                    return true; // 阻止事件继续传播
                }
            }
        }
    }

    // 对于其他事件，使用默认处理
    return QMainWindow::eventFilter(watched, event);
}

void MainWindow::installEventFilters() {
    // 为所有可能拦截鼠标事件的组件安装事件过滤器
    if (m_centralWidget) {
        m_centralWidget->installEventFilter(this);
    }

    if (m_titleBarFrame) {
        m_titleBarFrame->installEventFilter(this);
    }

    if (m_contentFrame) {
        m_contentFrame->installEventFilter(this);
    }

    if (m_statusBarFrame) {
        m_statusBarFrame->installEventFilter(this);
    }

    if (m_navigationFrame) {
        m_navigationFrame->installEventFilter(this);
    }

    if (m_workFrame) {
        m_workFrame->installEventFilter(this);
    }

    if (m_stackedWidget) {
        m_stackedWidget->installEventFilter(this);
    }

    // 为导航列表也安装事件过滤器
    if (m_navigationList) {
        m_navigationList->installEventFilter(this);
    }
}

void MainWindow::onControllerManualAdd()
{
    // 创建控制器添加对话框
    ControllerDialog* controllerDialog = new ControllerDialog(m_databaseProvider, this, ControllerDialog::Mode::Add);

    // 设置为非模态对话框
    controllerDialog->setWindowModality(Qt::NonModal);

    // 连接对话框的完成信号
    connect(controllerDialog, &QDialog::accepted, this, [this]() {
        qDebug() << "MainWindow: Controller dialog accepted, refreshing table...";

        // 确保切换到基本设置模块和控制器标签页
        switchToModule("基本设置");
        if (m_basicSettingsTab) {
            m_basicSettingsTab->setCurrentIndex(0); // 控制器标签页是第一个
        }

        // 立即刷新控制器列表
        refreshControllerTable();

        updateStatusBar();
        m_statusLabel->setText("控制器添加完成");

        // 3秒后恢复状态栏
        QTimer::singleShot(3000, this, [this]() {
            m_statusLabel->setText("就绪");
        });
    });

    connect(controllerDialog, &QDialog::rejected, this, [this]() {
        updateStatusBar();
        m_statusLabel->setText("取消添加控制器");

        // 3秒后恢复状态栏
        QTimer::singleShot(3000, this, [this]() {
            m_statusLabel->setText("就绪");
        });
    });

    // 设置对话框属性
    controllerDialog->setAttribute(Qt::WA_DeleteOnClose);

    // 显示对话框
    controllerDialog->show();

    // 更新状态栏
    updateStatusBar();
    m_statusLabel->setText("控制器添加对话框已打开");
}

void MainWindow::loadControllerData()
{
    qDebug() << "MainWindow::loadControllerData() called";

    if (!m_controllerTable) {
        qWarning() << "MainWindow: m_controllerTable is null";
        return;
    }

    if (!m_databaseProvider) {
        qWarning() << "MainWindow: m_databaseProvider is null";
        return;
    }

    try {
        // 创建ControllerDao
        ControllerDao controllerDao(m_databaseProvider);

        // 获取所有控制器
        QList<Controller> controllers = controllerDao.findAll();

        qDebug() << "MainWindow: Found" << controllers.size() << "controllers in database";

        // 清空现有数据
        m_controllerTable->clearContents();

        // 设置表格行数
        m_controllerTable->setRowCount(controllers.size());

        // 填充数据
        for (int i = 0; i < controllers.size(); ++i) {
            const Controller& controller = controllers[i];

            qDebug() << "MainWindow: Processing controller" << i << "- SN:" << controller.serialNumber();

            // 控制器编号
            m_controllerTable->setItem(i, 0, new QTableWidgetItem(QString::number(controller.controllerNumber())));

            // 序列号SN
            m_controllerTable->setItem(i, 1, new QTableWidgetItem(controller.serialNumber()));

            // 启用状态
            m_controllerTable->setItem(i, 2, new QTableWidgetItem(controller.enabled() ? "启用" : "禁用"));

            // IP地址
            m_controllerTable->setItem(i, 3, new QTableWidgetItem(controller.ipAddress()));

            // 端口
            m_controllerTable->setItem(i, 4, new QTableWidgetItem(QString::number(controller.port())));

            // 所在区域 - 查询区域名称和完整路径
            QString areaName = "未分配";
            if (controller.areaId() > 0) {
                AreaDao areaDao(m_databaseProvider);
                Area area = areaDao.findById(controller.areaId());
                if (area.id() > 0) {
                    if (area.fullPath().isEmpty()) {
                        areaName = area.name();
                    } else {
                        // 使用"\"显示区域的完整层级关系
                        areaName = area.fullPath().replace("/", "\\");
                    }
                } else {
                    areaName = QString("区域%1").arg(controller.areaId());
                }
            }
            m_controllerTable->setItem(i, 5, new QTableWidgetItem(areaName));

            // 说明
            m_controllerTable->setItem(i, 6, new QTableWidgetItem(controller.description()));

            // 控制的门
            QString doorInfo = QString("%1门控制器").arg(controller.getMaxDoors());
            m_controllerTable->setItem(i, 7, new QTableWidgetItem(doorInfo));
        }

        // 强制刷新表格显示
        m_controllerTable->update();
        m_controllerTable->repaint();

        qDebug() << "MainWindow: Controller table updated with" << controllers.size() << "rows";

    } catch (const std::exception& e) {
        qWarning() << "MainWindow: Failed to load controller data:" << e.what();
    }
}

void MainWindow::refreshControllerTable()
{
    qDebug() << "MainWindow::refreshControllerTable() called";
    loadControllerData();
}



} // namespace AccessControl
